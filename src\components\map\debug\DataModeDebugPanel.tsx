import React, { useState, useEffect } from 'react';

interface DataModeDebugInfo {
  mode: 'zip-areas' | 'points' | 'unknown';
  zipBoundariesLoaded: boolean;
  zipFeaturesCount: number;
  propertiesLoaded: boolean;
  propertiesCount: number;
  mergedFeaturesCount: number;
  lastUpdate: string;
}

/**
 * 数据模式调试面板
 * 显示当前地图数据加载状态和模式选择原因
 */
const DataModeDebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DataModeDebugInfo>({
    mode: 'unknown',
    zipBoundariesLoaded: false,
    zipFeaturesCount: 0,
    propertiesLoaded: false,
    propertiesCount: 0,
    mergedFeaturesCount: 0,
    lastUpdate: new Date().toISOString(),
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 监听数据加载事件
    const handleDataModeUpdate = (event: CustomEvent) => {
      setDebugInfo({
        ...event.detail,
        lastUpdate: new Date().toISOString(),
      });
    };

    window.addEventListener('dataModeUpdate', handleDataModeUpdate as EventListener);

    return () => {
      window.removeEventListener('dataModeUpdate', handleDataModeUpdate as EventListener);
    };
  }, []);

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'zip-areas': return '#22c55e'; // 绿色 - ZIP区域模式
      case 'points': return '#f59e0b'; // 橙色 - 圆点模式
      default: return '#6b7280'; // 灰色 - 未知
    }
  };

  const getModeText = (mode: string) => {
    switch (mode) {
      case 'zip-areas': return 'ZIP区域模式';
      case 'points': return '圆点模式';
      default: return '未知模式';
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 px-3 py-2 text-xs font-medium text-white bg-gray-600 rounded-lg hover:bg-gray-700 transition-colors"
        title="显示数据模式调试信息"
      >
        🔍 调试
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
          数据模式调试
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          ✕
        </button>
      </div>

      <div className="space-y-3">
        {/* 当前模式 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">当前模式:</span>
          <span 
            className="text-sm font-medium px-2 py-1 rounded"
            style={{ 
              backgroundColor: getModeColor(debugInfo.mode) + '20',
              color: getModeColor(debugInfo.mode)
            }}
          >
            {getModeText(debugInfo.mode)}
          </span>
        </div>

        {/* ZIP边界数据 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">ZIP边界:</span>
          <span className={`text-sm ${debugInfo.zipBoundariesLoaded ? 'text-green-600' : 'text-red-600'}`}>
            {debugInfo.zipBoundariesLoaded ? '✅ 已加载' : '❌ 未加载'}
          </span>
        </div>

        {/* ZIP要素数量 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">ZIP要素数:</span>
          <span className="text-sm font-mono text-gray-900 dark:text-white">
            {debugInfo.zipFeaturesCount.toLocaleString()}
          </span>
        </div>

        {/* 属性数据 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">属性数据:</span>
          <span className={`text-sm ${debugInfo.propertiesLoaded ? 'text-green-600' : 'text-red-600'}`}>
            {debugInfo.propertiesLoaded ? '✅ 已加载' : '❌ 未加载'}
          </span>
        </div>

        {/* 属性数量 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">属性数量:</span>
          <span className="text-sm font-mono text-gray-900 dark:text-white">
            {debugInfo.propertiesCount.toLocaleString()}
          </span>
        </div>

        {/* 合并结果 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">合并结果:</span>
          <span className="text-sm font-mono text-gray-900 dark:text-white">
            {debugInfo.mergedFeaturesCount.toLocaleString()}
          </span>
        </div>

        {/* 最后更新时间 */}
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            更新时间: {new Date(debugInfo.lastUpdate).toLocaleTimeString()}
          </span>
        </div>

        {/* 说明 */}
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {debugInfo.mode === 'points' && (
              <>
                <strong>圆点模式原因:</strong><br/>
                {!debugInfo.zipBoundariesLoaded && '• ZIP边界数据未加载'}<br/>
                {!debugInfo.propertiesLoaded && '• 属性数据未加载'}<br/>
                {debugInfo.mergedFeaturesCount === 0 && '• 数据合并失败'}
              </>
            )}
            {debugInfo.mode === 'zip-areas' && (
              <>
                <strong>ZIP区域模式:</strong> 数据合并成功
              </>
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

export default DataModeDebugPanel;
